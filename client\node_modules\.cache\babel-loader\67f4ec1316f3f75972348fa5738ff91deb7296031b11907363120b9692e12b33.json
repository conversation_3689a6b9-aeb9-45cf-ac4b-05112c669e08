{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"This question could not be loaded. Please try refreshing the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-red-50 rounded-xl p-6 border border-red-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-4xl mb-2\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3 sm:space-y-4 lg:space-y-5\",\n      children: Object.entries(questionData.options).map(([key, value], index) => {\n        const optionKey = safeString(key).trim();\n        const optionValue = safeString(value).trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => handleAnswerSelect(optionKey),\n          whileHover: {\n            scale: 1.01\n          },\n          whileTap: {\n            scale: 0.99\n          },\n          className: `w-full text-left p-4 sm:p-5 lg:p-6 rounded-lg sm:rounded-xl lg:rounded-2xl border-2 transition-all duration-300 touch-manipulation ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg transform scale-[1.02]' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 sm:gap-4 lg:gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center font-bold text-sm sm:text-base lg:text-lg transition-all flex-shrink-0 ${isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-700'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `flex-1 font-medium text-base sm:text-lg lg:text-xl leading-relaxed ${isSelected ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4 sm:space-y-5 lg:space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 lg:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg sm:text-xl lg:text-2xl\",\n          children: \"\\u270F\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Your Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full px-4 sm:px-5 lg:px-6 py-4 sm:py-5 lg:py-6 border-2 border-gray-200 rounded-lg sm:rounded-xl lg:rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-base sm:text-lg lg:text-xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\",\n        children: currentAnswer ? /*#__PURE__*/_jsxDEV(TbCheck, {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600\",\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${progressPercentage}%`\n        },\n        transition: {\n          duration: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex sm:hidden items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 text-center px-2\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-base font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\",\n            children: [questionIndex + 1, \"/\", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:grid sm:grid-cols-3 items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl lg:text-2xl font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 hidden lg:block\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 lg:px-6 py-2 lg:py-3 rounded-xl font-mono text-lg lg:text-xl font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border-2 border-blue-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden lg:inline\",\n                children: \"TIME\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 text-gray-700 px-3 lg:px-4 py-2 lg:py-3 rounded-lg text-sm lg:text-base font-semibold\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-lg p-4 sm:p-6 lg:p-10 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 sm:mb-6 lg:mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm lg:text-base font-semibold\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 lg:mb-10 leading-relaxed\",\n            children: questionData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 sm:mb-8 lg:mb-10 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 border border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full max-h-64 sm:max-h-80 lg:max-h-96 rounded-lg shadow-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-options\",\n            children: questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-navigation\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-3 sm:gap-4 lg:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 sm:gap-4 flex-1 justify-center\",\n            children: !isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 sm:gap-2 text-amber-600 bg-amber-50 px-2 sm:px-3 lg:px-4 py-1 sm:py-2 lg:py-3 rounded-lg text-xs sm:text-sm lg:text-base border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Select an answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Answer required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "handleAnswerSelect", "answer", "progressPercentage", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMCQ", "options", "Object", "keys", "length", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "trim", "optionValue", "label", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "div", "initial", "width", "animate", "transition", "duration", "opacity", "x", "image", "src", "alt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Available</h3>\n          <p className=\"text-gray-600\">This question could not be loaded. Please try refreshing the page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      return (\n        <div className=\"text-center bg-red-50 rounded-xl p-6 border border-red-200\">\n          <div className=\"text-red-500 text-4xl mb-2\">⚠️</div>\n          <p className=\"text-red-700\">No options available for this question.</p>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-3 sm:space-y-4 lg:space-y-5\">\n        {Object.entries(questionData.options).map(([key, value], index) => {\n          const optionKey = safeString(key).trim();\n          const optionValue = safeString(value).trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <motion.button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              whileHover={{ scale: 1.01 }}\n              whileTap={{ scale: 0.99 }}\n              className={`w-full text-left p-4 sm:p-5 lg:p-6 rounded-lg sm:rounded-xl lg:rounded-2xl border-2 transition-all duration-300 touch-manipulation ${\n                isSelected\n                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg transform scale-[1.02]'\n                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'\n              }`}\n            >\n              <div className=\"flex items-center gap-3 sm:gap-4 lg:gap-6\">\n                <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center font-bold text-sm sm:text-base lg:text-lg transition-all flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-white text-blue-600'\n                    : 'bg-blue-100 text-blue-700'\n                }`}>\n                  {label}\n                </div>\n                <span className={`flex-1 font-medium text-base sm:text-lg lg:text-xl leading-relaxed ${\n                  isSelected ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n                {isSelected && (\n                  <TbCheck className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\" />\n                )}\n              </div>\n            </motion.button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"space-y-4 sm:space-y-5 lg:space-y-6\">\n      <label className=\"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\">\n        <div className=\"flex items-center gap-2 lg:gap-3\">\n          <span className=\"text-lg sm:text-xl lg:text-2xl\">✏️</span>\n          <span>Your Answer:</span>\n        </div>\n      </label>\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full px-4 sm:px-5 lg:px-6 py-4 sm:py-5 lg:py-6 border-2 border-gray-200 rounded-lg sm:rounded-xl lg:rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-base sm:text-lg lg:text-xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg\"\n        />\n        <div className=\"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\">\n          {currentAnswer ? (\n            <TbCheck className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\" />\n          ) : (\n            <div className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"></div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {/* Progress Bar */}\n      <div className=\"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\">\n        <motion.div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600\"\n          initial={{ width: 0 }}\n          animate={{ width: `${progressPercentage}%` }}\n          transition={{ duration: 0.5 }}\n        />\n      </div>\n\n      {/* Enhanced Header with Better Navigation */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\">\n          {/* Mobile Layout */}\n          <div className=\"flex sm:hidden items-center justify-between\">\n            {/* Timer - Left */}\n            <div className={`flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n                : 'bg-blue-100 text-blue-700 border border-blue-300'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n\n            {/* Quiz Title - Center */}\n            <div className=\"flex-1 text-center px-2\">\n              <h1 className=\"text-base font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n            </div>\n\n            {/* Question Counter - Right */}\n            <div className=\"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\">\n              {questionIndex + 1}/{totalQuestions}\n            </div>\n          </div>\n\n          {/* Desktop Layout */}\n          <div className=\"hidden sm:grid sm:grid-cols-3 items-center gap-4\">\n            {/* Quiz Title */}\n            <div className=\"text-left\">\n              <h1 className=\"text-xl lg:text-2xl font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n              <p className=\"text-sm text-gray-600 hidden lg:block\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Centered Timer */}\n            <div className=\"flex justify-center\">\n              <div className={`flex items-center gap-2 px-4 lg:px-6 py-2 lg:py-3 rounded-xl font-mono text-lg lg:text-xl font-bold transition-all ${\n                isTimeWarning\n                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'\n                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'\n              }`}>\n                <TbClock className=\"w-5 h-5 lg:w-6 lg:h-6\" />\n                <span className=\"hidden lg:inline\">TIME</span>\n                <span>{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"flex justify-end\">\n              <div className=\"bg-gray-100 text-gray-700 px-3 lg:px-4 py-2 lg:py-3 rounded-lg text-sm lg:text-base font-semibold\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Scrollable Area */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-lg p-4 sm:p-6 lg:p-10 mb-6 sm:mb-8 lg:mb-12\"\n          >\n            {/* Question Number Badge */}\n            <div className=\"mb-4 sm:mb-6 lg:mb-8\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm lg:text-base font-semibold\">\n                <span>Question {questionIndex + 1}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div className=\"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 lg:mb-10 leading-relaxed\">\n              {questionData.name}\n            </div>\n\n            {/* Question Image */}\n            {questionData.image && (\n              <div className=\"mb-6 sm:mb-8 lg:mb-10 text-center\">\n                <div className=\"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 border border-gray-200\">\n                  <img\n                    src={questionData.image}\n                    alt=\"Question\"\n                    className=\"max-w-full max-h-64 sm:max-h-80 lg:max-h-96 rounded-lg shadow-md\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Question Content */}\n            <div className=\"quiz-options\">\n              {questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"quiz-navigation\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-8\">\n          <div className=\"flex items-center justify-between gap-3 sm:gap-4 lg:gap-6\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n            </button>\n\n            <div className=\"flex items-center gap-2 sm:gap-4 flex-1 justify-center\">\n              {!isAnswered && (\n                <div className=\"flex items-center gap-1 sm:gap-2 text-amber-600 bg-amber-50 px-2 sm:px-3 lg:px-4 py-1 sm:py-2 lg:py-3 rounded-lg text-xs sm:text-sm lg:text-base border border-amber-200\">\n                  <span>⚠️</span>\n                  <span className=\"hidden sm:inline\">Select an answer</span>\n                  <span className=\"sm:hidden\">Answer required</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base lg:text-lg touch-manipulation ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n                  <span className=\"hidden sm:inline\">Submit Quiz</span>\n                  <span className=\"sm:hidden\">Submit</span>\n                </>\n              ) : (\n                <>\n                  <span className=\"hidden sm:inline\">Next</span>\n                  <span className=\"sm:hidden\">Next</span>\n                  <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AACpF,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAACkB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAGvB,mBAAmB,CAACQ,QAAQ,CAAC;EAElDd,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMe,kBAAkB,GAAIC,MAAM,IAAK;IACrCL,gBAAgB,CAACK,MAAM,CAAC;IACxBH,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACa,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACjB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACa,YAAY,CAACI,IAAI,IAAIJ,YAAY,CAACI,IAAI,KAAK,wBAAwB,EAAE;IACxE,oBACEvB,OAAA;MAAKwB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGzB,OAAA;QAAKwB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DzB,OAAA;UAAKwB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD7B,OAAA;UAAIwB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF7B,OAAA;UAAGwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACX,YAAY,CAACY,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACd,YAAY,CAACY,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAC3E,oBACElC,OAAA;QAAKwB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzEzB,OAAA;UAAKwB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD7B,OAAA;UAAGwB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEV;IAEA,MAAMM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACEnC,OAAA;MAAKwB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EACjDO,MAAM,CAACI,OAAO,CAACjB,YAAY,CAACY,OAAO,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QACjE,MAAMC,SAAS,GAAG5C,UAAU,CAACyC,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACxC,MAAMC,WAAW,GAAG9C,UAAU,CAAC0C,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAC5C,MAAME,KAAK,GAAGT,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMI,UAAU,GAAG9B,aAAa,KAAK0B,SAAS;;QAE9C;QACA,IAAI,CAACE,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACE3C,OAAA,CAACT,MAAM,CAACuD,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAACqB,SAAS,CAAE;UAC7CO,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BzB,SAAS,EAAG,sIACVqB,UAAU,GACN,yEAAyE,GACzE,+FACL,EAAE;UAAApB,QAAA,eAEHzB,OAAA;YAAKwB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDzB,OAAA;cAAKwB,SAAS,EAAG,kKACfqB,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAApB,QAAA,EACAmB;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAMwB,SAAS,EAAG,sEAChBqB,UAAU,GAAG,YAAY,GAAG,eAC7B,EAAE;cAAApB,QAAA,EACAkB;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACNgB,UAAU,iBACT7C,OAAA,CAACL,OAAO;cAAC6B,SAAS,EAAC;YAA8D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACpF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BDY,SAAS;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BD,CAAC;MAEpB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMsB,eAAe,GAAGA,CAAA,kBACtBnD,OAAA;IAAKwB,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClDzB,OAAA;MAAOwB,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACrFzB,OAAA;QAAKwB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CzB,OAAA;UAAMwB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D7B,OAAA;UAAAyB,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACR7B,OAAA;MAAKwB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBzB,OAAA;QACEoD,IAAI,EAAC,MAAM;QACXb,KAAK,EAAExB,aAAc;QACrBsC,QAAQ,EAAGC,CAAC,IAAKlC,kBAAkB,CAACkC,CAAC,CAACC,MAAM,CAAChB,KAAK,CAAE;QACpDiB,WAAW,EAAC,0BAA0B;QACtChC,SAAS,EAAC;MAAwR;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnS,CAAC,eACF7B,OAAA;QAAKwB,SAAS,EAAC,2EAA2E;QAAAC,QAAA,EACvFV,aAAa,gBACZf,OAAA,CAACL,OAAO;UAAC6B,SAAS,EAAC;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE1E7B,OAAA;UAAKwB,SAAS,EAAC;QAA8D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACpF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,qFAAqF;IAAAC,QAAA,gBAElGzB,OAAA;MAAKwB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9DzB,OAAA,CAACT,MAAM,CAACkE,GAAG;QACTjC,SAAS,EAAC,qDAAqD;QAC/DkC,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBC,OAAO,EAAE;UAAED,KAAK,EAAG,GAAErC,kBAAmB;QAAG,CAAE;QAC7CuC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFzB,OAAA;QAAKwB,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1EzB,OAAA;UAAKwB,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAE1DzB,OAAA;YAAKwB,SAAS,EAAG,2FACfX,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;YAAAY,QAAA,gBACDzB,OAAA,CAACR,OAAO;cAACgC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B7B,OAAA;cAAAyB,QAAA,EAAO3B,UAAU,CAACW,QAAQ;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCzB,OAAA;cAAIwB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAE5B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAClFpB,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7B,OAAA;UAAKwB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DzB,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzB,OAAA;cAAIwB,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAE5B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzG7B,OAAA;cAAGwB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClCzB,OAAA;cAAKwB,SAAS,EAAG,sHACfX,aAAa,GACT,+DAA+D,GAC/D,oDACL,EAAE;cAAAY,QAAA,gBACDzB,OAAA,CAACR,OAAO;gBAACgC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C7B,OAAA;gBAAMwB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9C7B,OAAA;gBAAAyB,QAAA,EAAO3B,UAAU,CAACW,QAAQ;cAAC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BzB,OAAA;cAAKwB,SAAS,EAAC,mGAAmG;cAAAC,QAAA,GAAC,WACxG,EAACpB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCzB,OAAA;QAAKwB,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1EzB,OAAA,CAACT,MAAM,CAACkE,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BtC,SAAS,EAAC,sGAAsG;UAAAC,QAAA,gBAGhHzB,OAAA;YAAKwB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCzB,OAAA;cAAKwB,SAAS,EAAC,+LAA+L;cAAAC,QAAA,eAC5MzB,OAAA;gBAAAyB,QAAA,GAAM,WAAS,EAACpB,aAAa,GAAG,CAAC;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAC9GN,YAAY,CAACI;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAGLV,YAAY,CAAC8C,KAAK,iBACjBjE,OAAA;YAAKwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDzB,OAAA;cAAKwB,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxGzB,OAAA;gBACEkE,GAAG,EAAE/C,YAAY,CAAC8C,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACd3C,SAAS,EAAC;cAAkE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD7B,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BN,YAAY,CAACiC,IAAI,KAAK,KAAK,IAAIpB,MAAM,CAACC,IAAI,CAACd,YAAY,CAACY,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GAAGJ,SAAS,CAAC,CAAC,GAAGqB,eAAe,CAAC;UAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC;QAAA,GAlCDxB,aAAa;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDzB,OAAA;UAAKwB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEzB,OAAA;YACE+C,OAAO,EAAEpC,UAAW;YACpByD,QAAQ,EAAE/D,aAAa,KAAK,CAAE;YAC9BmB,SAAS,EAAG,uLACVnB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAAoB,QAAA,gBAEHzB,OAAA,CAACP,WAAW;cAAC+B,SAAS,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D7B,OAAA;cAAMwB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAET7B,OAAA;YAAKwB,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACpE,CAACR,UAAU,iBACVjB,OAAA;cAAKwB,SAAS,EAAC,0KAA0K;cAAAC,QAAA,gBACvLzB,OAAA;gBAAAyB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf7B,OAAA;gBAAMwB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D7B,OAAA;gBAAMwB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN7B,OAAA;YACE+C,OAAO,EAAErC,MAAO;YAChB0D,QAAQ,EAAE,CAACnD,UAAW;YACtBO,SAAS,EAAG,uLACV,CAACP,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4DAA4D,GAC5D,0DACP,EAAE;YAAAmB,QAAA,EAEFpB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAuB,QAAA,gBACEzB,OAAA,CAACL,OAAO;gBAAC6B,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3D7B,OAAA;gBAAMwB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD7B,OAAA;gBAAMwB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACzC,CAAC,gBAEH7B,OAAA,CAAAE,SAAA;cAAAuB,QAAA,gBACEzB,OAAA;gBAAMwB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9C7B,OAAA;gBAAMwB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC7B,OAAA,CAACN,YAAY;gBAAC8B,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eAChE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CA3SIX,YAAY;AAAAkE,EAAA,GAAZlE,YAAY;AA6SlB,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}