import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';
import { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  // Extract safe question data to prevent object rendering errors
  const questionData = extractQuestionData(question);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Check if question data is valid
  if (!question) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">No Question Data</h3>
          <p className="text-gray-600">Question data is missing. Please check the quiz configuration.</p>
        </div>
      </div>
    );
  }

  // Early return for invalid question
  if (!questionData.name || questionData.name === 'Question not available') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Available</h3>
          <p className="text-gray-600">This question could not be loaded. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  const renderMCQ = () => {
    if (!questionData.options || Object.keys(questionData.options).length === 0) {
      return (
        <div className="text-center bg-red-50 rounded-xl p-6 border border-red-200">
          <div className="text-red-500 text-4xl mb-2">⚠️</div>
          <p className="text-red-700">No options available for this question.</p>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5">
        {Object.entries(questionData.options).map(([key, value], index) => {
          const optionKey = safeString(key).trim();
          const optionValue = safeString(value).trim();
          const label = optionLabels[index] || optionKey;
          const isSelected = currentAnswer === optionKey;

          // Skip empty options
          if (!optionValue) return null;

          return (
            <motion.button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              whileHover={{ scale: 1.005 }}
              whileTap={{ scale: 0.995 }}
              className={`w-full text-left p-3 sm:p-4 md:p-5 lg:p-6 xl:p-7 rounded-lg sm:rounded-xl md:rounded-2xl border-2 transition-all duration-300 touch-manipulation min-h-[56px] sm:min-h-[64px] md:min-h-[72px] lg:min-h-[80px] xl:min-h-[88px] quiz-option ${
                isSelected
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300 selected'
                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md active:bg-blue-100'
              }`}
            >
              <div className="flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-5">
                <div className={`w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm md:text-base lg:text-lg transition-all flex-shrink-0 quiz-option-letter ${
                  isSelected
                    ? 'bg-white text-blue-600 shadow-md'
                    : 'bg-blue-100 text-blue-700'
                }`}>
                  {label}
                </div>
                <span className={`flex-1 font-medium text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-relaxed break-words quiz-option-text ${
                  isSelected ? 'text-white' : 'text-gray-800'
                }`}>
                  {optionValue}
                </span>
                {isSelected && (
                  <TbCheck className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0" />
                )}
              </div>
            </motion.button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="space-y-4 sm:space-y-5 lg:space-y-6">
      <label className="block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3">
        <div className="flex items-center gap-2 lg:gap-3">
          <span className="text-lg sm:text-xl lg:text-2xl">✏️</span>
          <span>Your Answer:</span>
        </div>
      </label>
      <div className="relative">
        <input
          type="text"
          value={currentAnswer}
          onChange={(e) => handleAnswerSelect(e.target.value)}
          placeholder="Type your answer here..."
          className="w-full px-3 sm:px-4 md:px-5 lg:px-6 xl:px-7 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 border-2 border-gray-200 rounded-lg sm:rounded-xl md:rounded-2xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg min-h-[50px] sm:min-h-[56px] md:min-h-[64px] lg:min-h-[72px] xl:min-h-[80px] touch-manipulation quiz-fill-input"
        />
        <div className="absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2">
          {currentAnswer ? (
            <TbCheck className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500" />
          ) : (
            <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full"></div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="quiz-container quiz-renderer h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden fixed inset-0 z-50">
      {/* Progress Bar */}
      <div className="fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>

      {/* Enhanced Header with Better Navigation */}
      <div className="bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4">
          {/* Mobile Layout (< 640px) */}
          <div className="flex sm:hidden items-center justify-between">
            {/* Timer - Left */}
            <div className={`flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${
              isTimeWarning
                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'
                : 'bg-blue-100 text-blue-700 border border-blue-300'
            }`}>
              <TbClock className="w-4 h-4" />
              <span>{formatTime(timeLeft)}</span>
            </div>

            {/* Quiz Title - Center */}
            <div className="flex-1 text-center px-2">
              <h1 className="text-base font-bold text-gray-900 truncate">{safeString(examTitle, 'Quiz')}</h1>
            </div>

            {/* Question Counter - Right */}
            <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold">
              {questionIndex + 1}/{totalQuestions}
            </div>
          </div>

          {/* Tablet Layout (640px - 1024px) */}
          <div className="hidden sm:flex lg:hidden items-center justify-between gap-3">
            {/* Quiz Title */}
            <div className="flex-1 text-left">
              <h1 className="text-lg font-bold text-gray-900 truncate">{safeString(examTitle, 'Quiz')}</h1>
              <p className="text-sm text-gray-600">Challenge your brain, beat the rest</p>
            </div>

            {/* Timer */}
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-base font-bold transition-all ${
              isTimeWarning
                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'
                : 'bg-blue-100 text-blue-700 border border-blue-300'
            }`}>
              <TbClock className="w-4 h-4" />
              <span>{formatTime(timeLeft)}</span>
            </div>

            {/* Question Counter */}
            <div className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold">
              {questionIndex + 1} of {totalQuestions}
            </div>
          </div>

          {/* Desktop Layout (>= 1024px) */}
          <div className="hidden lg:grid lg:grid-cols-3 items-center gap-4">
            {/* Quiz Title */}
            <div className="text-left">
              <h1 className="text-xl xl:text-2xl font-bold text-gray-900 truncate">{safeString(examTitle, 'Quiz')}</h1>
              <p className="text-sm text-gray-600">Challenge your brain, beat the rest</p>
            </div>

            {/* Centered Timer */}
            <div className="flex justify-center">
              <div className={`flex items-center gap-2 px-4 xl:px-6 py-2 xl:py-3 rounded-xl font-mono text-lg xl:text-xl font-bold transition-all ${
                isTimeWarning
                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'
                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'
              }`}>
                <TbClock className="w-5 h-5 xl:w-6 xl:h-6" />
                <span>TIME</span>
                <span>{formatTime(timeLeft)}</span>
              </div>
            </div>

            {/* Question Counter */}
            <div className="flex justify-end">
              <div className="bg-gray-100 text-gray-700 px-3 xl:px-4 py-2 xl:py-3 rounded-lg text-sm xl:text-base font-semibold">
                Question {questionIndex + 1} of {totalQuestions}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable Area */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-6xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6 lg:py-8">
          <motion.div
            key={questionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-xl p-3 sm:p-4 md:p-6 lg:p-8 xl:p-10 mb-4 sm:mb-6 lg:mb-8 border border-gray-100"

          >
            {/* Question Number Badge */}
            <div className="mb-3 sm:mb-4 md:mb-6 lg:mb-8">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm md:text-base font-semibold shadow-lg">
                <span>Question {questionIndex + 1} of {totalQuestions}</span>
              </div>
            </div>

            {/* Question Text */}
            <div className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-4 sm:mb-5 md:mb-6 lg:mb-8 leading-relaxed quiz-question-text">
              {questionData.name}
            </div>

            {/* Question Image */}
            {questionData.image && (
              <div className="mb-4 sm:mb-5 md:mb-6 lg:mb-8 text-center quiz-image-container-modern">
                <div className="inline-block bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 lg:p-6 border border-gray-200 shadow-sm quiz-image-wrapper">
                  <img
                    src={questionData.image}
                    alt="Question"
                    className="max-w-full h-auto max-h-40 sm:max-h-48 md:max-h-64 lg:max-h-80 xl:max-h-96 rounded-lg shadow-md object-contain quiz-image-modern"
                  />
                </div>
              </div>
            )}

            {/* Question Content */}
            <div className="quiz-options">
              {questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced Bottom Navigation */}
      <div className="bg-white border-t border-gray-200 shadow-lg flex-shrink-0">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-3 sm:py-4">
          {/* Mobile Navigation */}
          <div className="flex sm:hidden items-center justify-between gap-2">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'
              }`}
            >
              <TbArrowLeft className="w-4 h-4" />
              <span>Prev</span>
            </button>

            {/* Center Status */}
            <div className="flex-1 flex justify-center">
              {!isAnswered ? (
                <div className="flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-lg text-xs border border-amber-200">
                  <span>⚠️</span>
                  <span>Select answer</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-lg text-xs border border-green-200">
                  <TbCheck className="w-3 h-3" />
                  <span>Answered</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'
                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-4 h-4" />
                  <span>Submit</span>
                </>
              ) : (
                <>
                  <span>Next</span>
                  <TbArrowRight className="w-4 h-4" />
                </>
              )}
            </button>
          </div>

          {/* Tablet Navigation (640px - 1024px) */}
          <div className="hidden sm:flex lg:hidden items-center justify-between gap-3">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'
              }`}
            >
              <TbArrowLeft className="w-4 h-4" />
              <span>Previous</span>
            </button>

            {/* Center Status */}
            <div className="flex-1 flex justify-center">
              {!isAnswered ? (
                <div className="flex items-center gap-1 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-xs border border-amber-200">
                  <span>⚠️</span>
                  <span>Select answer</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-lg text-xs border border-green-200">
                  <TbCheck className="w-4 h-4" />
                  <span>Ready</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'
                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-4 h-4" />
                  <span>Submit</span>
                </>
              ) : (
                <>
                  <span>Next</span>
                  <TbArrowRight className="w-4 h-4" />
                </>
              )}
            </button>
          </div>

          {/* Desktop Navigation (>= 1024px) */}
          <div className="hidden lg:flex items-center justify-between gap-4">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'
              }`}
            >
              <TbArrowLeft className="w-5 h-5 xl:w-6 xl:h-6" />
              <span>Previous</span>
            </button>

            {/* Center Status */}
            <div className="flex-1 flex justify-center">
              {!isAnswered ? (
                <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-amber-200">
                  <span>⚠️</span>
                  <span>Please select an answer to continue</span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-green-600 bg-green-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-green-200">
                  <TbCheck className="w-5 h-5" />
                  <span>Answer selected - ready to proceed</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'
                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-5 h-5 xl:w-6 xl:h-6" />
                  <span>Submit Quiz</span>
                </>
              ) : (
                <>
                  <span>Next Question</span>
                  <TbArrowRight className="w-5 h-5 xl:w-6 xl:h-6" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizRenderer;
