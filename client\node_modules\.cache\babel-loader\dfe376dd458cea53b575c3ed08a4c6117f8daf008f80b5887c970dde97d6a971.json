{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\QuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>lock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n  const handleAnswerSelect = answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n  const progressPercentage = (questionIndex + 1) / totalQuestions * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"No Question Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Question data is missing. Please check the quiz configuration.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-6xl mb-4\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2 text-gray-900\",\n          children: \"Question Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"This question could not be loaded. Please try refreshing the page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this);\n  }\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-red-50 rounded-xl p-6 border border-red-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500 text-4xl mb-2\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700\",\n          children: \"No options available for this question.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this);\n    }\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5\",\n      children: Object.entries(questionData.options).map(([key, value], index) => {\n        const optionKey = safeString(key).trim();\n        const optionValue = safeString(value).trim();\n        const label = optionLabels[index] || optionKey;\n        const isSelected = currentAnswer === optionKey;\n\n        // Skip empty options\n        if (!optionValue) return null;\n        return /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => handleAnswerSelect(optionKey),\n          whileHover: {\n            scale: 1.005\n          },\n          whileTap: {\n            scale: 0.995\n          },\n          className: `w-full text-left p-3 sm:p-4 md:p-5 lg:p-6 xl:p-7 rounded-lg sm:rounded-xl md:rounded-2xl border-2 transition-all duration-300 touch-manipulation min-h-[56px] sm:min-h-[64px] md:min-h-[72px] lg:min-h-[80px] xl:min-h-[88px] quiz-option ${isSelected ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300 selected' : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md active:bg-blue-100'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm md:text-base lg:text-lg transition-all flex-shrink-0 quiz-option-letter ${isSelected ? 'bg-white text-blue-600 shadow-md' : 'bg-blue-100 text-blue-700'}`,\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `flex-1 font-medium text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-relaxed break-words quiz-option-text ${isSelected ? 'text-white' : 'text-gray-800'}`,\n              children: optionValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this), isSelected && /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this)\n        }, optionKey, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFillBlank = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4 sm:space-y-5 lg:space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 lg:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg sm:text-xl lg:text-2xl\",\n          children: \"\\u270F\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Your Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentAnswer,\n        onChange: e => handleAnswerSelect(e.target.value),\n        placeholder: \"Type your answer here...\",\n        className: \"w-full px-3 sm:px-4 md:px-5 lg:px-6 xl:px-7 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 border-2 border-gray-200 rounded-lg sm:rounded-xl md:rounded-2xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg min-h-[50px] sm:min-h-[56px] md:min-h-[64px] lg:min-h-[72px] xl:min-h-[80px] touch-manipulation quiz-fill-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\",\n        children: currentAnswer ? /*#__PURE__*/_jsxDEV(TbCheck, {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-container quiz-renderer h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"h-full bg-gradient-to-r from-blue-500 to-indigo-600\",\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${progressPercentage}%`\n        },\n        transition: {\n          duration: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex sm:hidden items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 text-center px-2\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-base font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\",\n            children: [questionIndex + 1, \"/\", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex lg:hidden items-center justify-between gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-base font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border border-blue-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\",\n            children: [questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:grid lg:grid-cols-3 items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl xl:text-2xl font-bold text-gray-900 truncate\",\n              children: safeString(examTitle, 'Quiz')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Challenge your brain, beat the rest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 px-4 xl:px-6 py-2 xl:py-3 rounded-xl font-mono text-lg xl:text-xl font-bold transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse' : 'bg-blue-100 text-blue-700 border-2 border-blue-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"TIME\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 text-gray-700 px-3 xl:px-4 py-2 xl:py-3 rounded-lg text-sm xl:text-base font-semibold\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-xl p-3 sm:p-4 md:p-6 lg:p-8 xl:p-10 mb-4 sm:mb-6 lg:mb-8 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 sm:mb-4 md:mb-6 lg:mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm md:text-base font-semibold shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-4 sm:mb-5 md:mb-6 lg:mb-8 leading-relaxed quiz-question-text\",\n            children: questionData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), questionData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 sm:mb-5 md:mb-6 lg:mb-8 text-center quiz-image-container-modern\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 lg:p-6 border border-gray-200 shadow-sm quiz-image-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: questionData.image,\n                alt: \"Question\",\n                className: \"max-w-full h-auto max-h-40 sm:max-h-48 md:max-h-64 lg:max-h-80 xl:max-h-96 rounded-lg shadow-md object-contain quiz-image-modern\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-options\",\n            children: questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t border-gray-200 shadow-lg flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex sm:hidden items-center justify-between gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-lg text-xs border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Select answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-lg text-xs border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-3 h-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answered\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex lg:hidden items-center justify-between gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-xs border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Select answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-lg text-xs border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Ready\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onPrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5 xl:w-6 xl:h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: !isAnswered ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-amber-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u26A0\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Please select an answer to continue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-green-600 bg-green-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Answer selected - ready to proceed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onNext,\n            disabled: !isAnswered,\n            className: `flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${!isAnswered ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : questionIndex === totalQuestions - 1 ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'}`,\n            children: questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Submit Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Next Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5 xl:w-6 xl:h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizRenderer, \"GLXCrRLAt2Wgb0CPI+PSeCbLXgs=\");\n_c = QuizRenderer;\nexport default QuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "extractQuestionData", "safeString", "formatTime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "handleAnswerSelect", "answer", "progressPercentage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "renderMCQ", "options", "Object", "keys", "length", "optionLabels", "entries", "map", "key", "value", "index", "optionKey", "trim", "optionValue", "label", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "renderFillBlank", "type", "onChange", "e", "target", "placeholder", "div", "initial", "width", "animate", "transition", "duration", "opacity", "x", "image", "src", "alt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/QuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';\nimport '../pages/user/Quiz/responsive.css';\n\nconst QuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // Extract safe question data to prevent object rendering errors\n  const questionData = extractQuestionData(question);\n\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  const handleAnswerSelect = (answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(true);\n    onAnswerChange(answer);\n  };\n\n  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;\n\n  // Check if question data is valid\n  if (!question) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">No Question Data</h3>\n          <p className=\"text-gray-600\">Question data is missing. Please check the quiz configuration.</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Early return for invalid question\n  if (!questionData.name || questionData.name === 'Question not available') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center bg-white rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold mb-2 text-gray-900\">Question Not Available</h3>\n          <p className=\"text-gray-600\">This question could not be loaded. Please try refreshing the page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const renderMCQ = () => {\n    if (!questionData.options || Object.keys(questionData.options).length === 0) {\n      return (\n        <div className=\"text-center bg-red-50 rounded-xl p-6 border border-red-200\">\n          <div className=\"text-red-500 text-4xl mb-2\">⚠️</div>\n          <p className=\"text-red-700\">No options available for this question.</p>\n        </div>\n      );\n    }\n\n    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];\n\n    return (\n      <div className=\"space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5\">\n        {Object.entries(questionData.options).map(([key, value], index) => {\n          const optionKey = safeString(key).trim();\n          const optionValue = safeString(value).trim();\n          const label = optionLabels[index] || optionKey;\n          const isSelected = currentAnswer === optionKey;\n\n          // Skip empty options\n          if (!optionValue) return null;\n\n          return (\n            <motion.button\n              key={optionKey}\n              onClick={() => handleAnswerSelect(optionKey)}\n              whileHover={{ scale: 1.005 }}\n              whileTap={{ scale: 0.995 }}\n              className={`w-full text-left p-3 sm:p-4 md:p-5 lg:p-6 xl:p-7 rounded-lg sm:rounded-xl md:rounded-2xl border-2 transition-all duration-300 touch-manipulation min-h-[56px] sm:min-h-[64px] md:min-h-[72px] lg:min-h-[80px] xl:min-h-[88px] quiz-option ${\n                isSelected\n                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300 selected'\n                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md active:bg-blue-100'\n              }`}\n            >\n              <div className=\"flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-5\">\n                <div className={`w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm md:text-base lg:text-lg transition-all flex-shrink-0 quiz-option-letter ${\n                  isSelected\n                    ? 'bg-white text-blue-600 shadow-md'\n                    : 'bg-blue-100 text-blue-700'\n                }`}>\n                  {label}\n                </div>\n                <span className={`flex-1 font-medium text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-relaxed break-words quiz-option-text ${\n                  isSelected ? 'text-white' : 'text-gray-800'\n                }`}>\n                  {optionValue}\n                </span>\n                {isSelected && (\n                  <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white flex-shrink-0\" />\n                )}\n              </div>\n            </motion.button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderFillBlank = () => (\n    <div className=\"space-y-4 sm:space-y-5 lg:space-y-6\">\n      <label className=\"block text-sm sm:text-base lg:text-lg font-medium text-gray-700 mb-3\">\n        <div className=\"flex items-center gap-2 lg:gap-3\">\n          <span className=\"text-lg sm:text-xl lg:text-2xl\">✏️</span>\n          <span>Your Answer:</span>\n        </div>\n      </label>\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={currentAnswer}\n          onChange={(e) => handleAnswerSelect(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full px-3 sm:px-4 md:px-5 lg:px-6 xl:px-7 py-3 sm:py-4 md:py-5 lg:py-6 xl:py-7 border-2 border-gray-200 rounded-lg sm:rounded-xl md:rounded-2xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg min-h-[50px] sm:min-h-[56px] md:min-h-[64px] lg:min-h-[72px] xl:min-h-[80px] touch-manipulation quiz-fill-input\"\n        />\n        <div className=\"absolute right-4 sm:right-5 lg:right-6 top-1/2 transform -translate-y-1/2\">\n          {currentAnswer ? (\n            <TbCheck className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-green-500\" />\n          ) : (\n            <div className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-200 rounded-full\"></div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"quiz-container quiz-renderer h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {/* Progress Bar */}\n      <div className=\"fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50\">\n        <motion.div\n          className=\"h-full bg-gradient-to-r from-blue-500 to-indigo-600\"\n          initial={{ width: 0 }}\n          animate={{ width: `${progressPercentage}%` }}\n          transition={{ duration: 0.5 }}\n        />\n      </div>\n\n      {/* Enhanced Header with Better Navigation */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200 pt-1 flex-shrink-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4\">\n          {/* Mobile Layout (< 640px) */}\n          <div className=\"flex sm:hidden items-center justify-between\">\n            {/* Timer - Left */}\n            <div className={`flex items-center gap-1 px-2 py-1 rounded-lg font-mono text-sm font-bold transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n                : 'bg-blue-100 text-blue-700 border border-blue-300'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n\n            {/* Quiz Title - Center */}\n            <div className=\"flex-1 text-center px-2\">\n              <h1 className=\"text-base font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n            </div>\n\n            {/* Question Counter - Right */}\n            <div className=\"bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs font-semibold\">\n              {questionIndex + 1}/{totalQuestions}\n            </div>\n          </div>\n\n          {/* Tablet Layout (640px - 1024px) */}\n          <div className=\"hidden sm:flex lg:hidden items-center justify-between gap-3\">\n            {/* Quiz Title */}\n            <div className=\"flex-1 text-left\">\n              <h1 className=\"text-lg font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n              <p className=\"text-sm text-gray-600\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-base font-bold transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'\n                : 'bg-blue-100 text-blue-700 border border-blue-300'\n            }`}>\n              <TbClock className=\"w-4 h-4\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold\">\n              {questionIndex + 1} of {totalQuestions}\n            </div>\n          </div>\n\n          {/* Desktop Layout (>= 1024px) */}\n          <div className=\"hidden lg:grid lg:grid-cols-3 items-center gap-4\">\n            {/* Quiz Title */}\n            <div className=\"text-left\">\n              <h1 className=\"text-xl xl:text-2xl font-bold text-gray-900 truncate\">{safeString(examTitle, 'Quiz')}</h1>\n              <p className=\"text-sm text-gray-600\">Challenge your brain, beat the rest</p>\n            </div>\n\n            {/* Centered Timer */}\n            <div className=\"flex justify-center\">\n              <div className={`flex items-center gap-2 px-4 xl:px-6 py-2 xl:py-3 rounded-xl font-mono text-lg xl:text-xl font-bold transition-all ${\n                isTimeWarning\n                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse'\n                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'\n              }`}>\n                <TbClock className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                <span>TIME</span>\n                <span>{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n\n            {/* Question Counter */}\n            <div className=\"flex justify-end\">\n              <div className=\"bg-gray-100 text-gray-700 px-3 xl:px-4 py-2 xl:py-3 rounded-lg text-sm xl:text-base font-semibold\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Scrollable Area */}\n      <div className=\"flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"max-w-6xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4 md:py-6 lg:py-8\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl shadow-xl p-3 sm:p-4 md:p-6 lg:p-8 xl:p-10 mb-4 sm:mb-6 lg:mb-8 border border-gray-100\"\n\n          >\n            {/* Question Number Badge */}\n            <div className=\"mb-3 sm:mb-4 md:mb-6 lg:mb-8\">\n              <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 lg:py-3 rounded-full text-xs sm:text-sm md:text-base font-semibold shadow-lg\">\n                <span>Question {questionIndex + 1} of {totalQuestions}</span>\n              </div>\n            </div>\n\n            {/* Question Text */}\n            <div className=\"text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-4 sm:mb-5 md:mb-6 lg:mb-8 leading-relaxed quiz-question-text\">\n              {questionData.name}\n            </div>\n\n            {/* Question Image */}\n            {questionData.image && (\n              <div className=\"mb-4 sm:mb-5 md:mb-6 lg:mb-8 text-center quiz-image-container-modern\">\n                <div className=\"inline-block bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 md:p-4 lg:p-6 border border-gray-200 shadow-sm quiz-image-wrapper\">\n                  <img\n                    src={questionData.image}\n                    alt=\"Question\"\n                    className=\"max-w-full h-auto max-h-40 sm:max-h-48 md:max-h-64 lg:max-h-80 xl:max-h-96 rounded-lg shadow-md object-contain quiz-image-modern\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Question Content */}\n            <div className=\"quiz-options\">\n              {questionData.type === 'mcq' || Object.keys(questionData.options).length > 0 ? renderMCQ() : renderFillBlank()}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Enhanced Bottom Navigation */}\n      <div className=\"bg-white border-t border-gray-200 shadow-lg flex-shrink-0\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          {/* Mobile Navigation */}\n          <div className=\"flex sm:hidden items-center justify-between gap-2\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4\" />\n              <span>Prev</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-lg text-xs border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Select answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-lg text-xs border border-green-200\">\n                  <TbCheck className=\"w-3 h-3\" />\n                  <span>Answered</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-semibold transition-all text-sm min-w-[80px] touch-manipulation ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Submit</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-4 h-4\" />\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Tablet Navigation (640px - 1024px) */}\n          <div className=\"hidden sm:flex lg:hidden items-center justify-between gap-3\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-1 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-xs border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Select answer</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-1 text-green-600 bg-green-50 px-3 py-2 rounded-lg text-xs border border-green-200\">\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Ready</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all text-sm touch-manipulation min-w-[100px] justify-center ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-4 h-4\" />\n                  <span>Submit</span>\n                </>\n              ) : (\n                <>\n                  <span>Next</span>\n                  <TbArrowRight className=\"w-4 h-4\" />\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Desktop Navigation (>= 1024px) */}\n          <div className=\"hidden lg:flex items-center justify-between gap-4\">\n            <button\n              onClick={onPrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md active:scale-95'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n              <span>Previous</span>\n            </button>\n\n            {/* Center Status */}\n            <div className=\"flex-1 flex justify-center\">\n              {!isAnswered ? (\n                <div className=\"flex items-center gap-2 text-amber-600 bg-amber-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-amber-200\">\n                  <span>⚠️</span>\n                  <span>Please select an answer to continue</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center gap-2 text-green-600 bg-green-50 px-4 xl:px-6 py-2 xl:py-3 rounded-lg text-sm xl:text-base border border-green-200\">\n                  <TbCheck className=\"w-5 h-5\" />\n                  <span>Answer selected - ready to proceed</span>\n                </div>\n              )}\n            </div>\n\n            <button\n              onClick={onNext}\n              disabled={!isAnswered}\n              className={`flex items-center gap-2 px-6 xl:px-8 py-3 xl:py-4 rounded-xl font-semibold transition-all text-base xl:text-lg touch-manipulation min-w-[120px] justify-center ${\n                !isAnswered\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : questionIndex === totalQuestions - 1\n                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg active:scale-95'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg active:scale-95'\n              }`}\n            >\n              {questionIndex === totalQuestions - 1 ? (\n                <>\n                  <TbCheck className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                  <span>Submit Quiz</span>\n                </>\n              ) : (\n                <>\n                  <span>Next Question</span>\n                  <TbArrowRight className=\"w-5 h-5 xl:w-6 xl:h-6\" />\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,wBAAwB;AACpF,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,QAAQ;EACRC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAACkB,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM8B,YAAY,GAAGvB,mBAAmB,CAACQ,QAAQ,CAAC;EAElDd,SAAS,CAAC,MAAM;IACd0B,gBAAgB,CAACT,cAAc,IAAI,EAAE,CAAC;IACtCW,aAAa,CAAC,CAAC,CAACX,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;EAEnC,MAAMe,kBAAkB,GAAIC,MAAM,IAAK;IACrCL,gBAAgB,CAACK,MAAM,CAAC;IACxBH,aAAa,CAAC,IAAI,CAAC;IACnBV,cAAc,CAACa,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAI,CAACjB,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG;;EAEvE;EACA,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEJ,OAAA;MAAKuB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGxB,OAAA;QAAKuB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DxB,OAAA;UAAKuB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD5B,OAAA;UAAIuB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E5B,OAAA;UAAGuB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACT,YAAY,CAACU,IAAI,IAAIV,YAAY,CAACU,IAAI,KAAK,wBAAwB,EAAE;IACxE,oBACE7B,OAAA;MAAKuB,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGxB,OAAA;QAAKuB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DxB,OAAA;UAAKuB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD5B,OAAA;UAAIuB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF5B,OAAA;UAAGuB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACX,YAAY,CAACY,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACd,YAAY,CAACY,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MAC3E,oBACElC,OAAA;QAAKuB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzExB,OAAA;UAAKuB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpD5B,OAAA;UAAGuB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEV;IAEA,MAAMO,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEnD,oBACEnC,OAAA;MAAKuB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,EAC9DQ,MAAM,CAACI,OAAO,CAACjB,YAAY,CAACY,OAAO,CAAC,CAACM,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEC,KAAK,KAAK;QACjE,MAAMC,SAAS,GAAG5C,UAAU,CAACyC,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;QACxC,MAAMC,WAAW,GAAG9C,UAAU,CAAC0C,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;QAC5C,MAAME,KAAK,GAAGT,YAAY,CAACK,KAAK,CAAC,IAAIC,SAAS;QAC9C,MAAMI,UAAU,GAAG9B,aAAa,KAAK0B,SAAS;;QAE9C;QACA,IAAI,CAACE,WAAW,EAAE,OAAO,IAAI;QAE7B,oBACE3C,OAAA,CAACT,MAAM,CAACuD,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAACqB,SAAS,CAAE;UAC7CO,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAM,CAAE;UAC7BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAM,CAAE;UAC3B1B,SAAS,EAAG,6OACVsB,UAAU,GACN,gFAAgF,GAChF,kHACL,EAAE;UAAArB,QAAA,eAEHxB,OAAA;YAAKuB,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjExB,OAAA;cAAKuB,SAAS,EAAG,4NACfsB,UAAU,GACN,kCAAkC,GAClC,2BACL,EAAE;cAAArB,QAAA,EACAoB;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5B,OAAA;cAAMuB,SAAS,EAAG,0HAChBsB,UAAU,GAAG,YAAY,GAAG,eAC7B,EAAE;cAAArB,QAAA,EACAmB;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACNiB,UAAU,iBACT7C,OAAA,CAACL,OAAO;cAAC4B,SAAS,EAAC;YAA4E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BDa,SAAS;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BD,CAAC;MAEpB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,kBACtBnD,OAAA;IAAKuB,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClDxB,OAAA;MAAOuB,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACrFxB,OAAA;QAAKuB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CxB,OAAA;UAAMuB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D5B,OAAA;UAAAwB,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACR5B,OAAA;MAAKuB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBxB,OAAA;QACEoD,IAAI,EAAC,MAAM;QACXb,KAAK,EAAExB,aAAc;QACrBsC,QAAQ,EAAGC,CAAC,IAAKlC,kBAAkB,CAACkC,CAAC,CAACC,MAAM,CAAChB,KAAK,CAAE;QACpDiB,WAAW,EAAC,0BAA0B;QACtCjC,SAAS,EAAC;MAA+b;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1c,CAAC,eACF5B,OAAA;QAAKuB,SAAS,EAAC,2EAA2E;QAAAC,QAAA,EACvFT,aAAa,gBACZf,OAAA,CAACL,OAAO;UAAC4B,SAAS,EAAC;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE1E5B,OAAA;UAAKuB,SAAS,EAAC;QAA8D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACpF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE5B,OAAA;IAAKuB,SAAS,EAAC,kHAAkH;IAAAC,QAAA,gBAE/HxB,OAAA;MAAKuB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9DxB,OAAA,CAACT,MAAM,CAACkE,GAAG;QACTlC,SAAS,EAAC,qDAAqD;QAC/DmC,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBC,OAAO,EAAE;UAAED,KAAK,EAAG,GAAErC,kBAAmB;QAAG,CAAE;QAC7CuC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5B,OAAA;MAAKuB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFxB,OAAA;QAAKuB,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1ExB,OAAA;UAAKuB,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAE1DxB,OAAA;YAAKuB,SAAS,EAAG,2FACfV,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;YAAAW,QAAA,gBACDxB,OAAA,CAACR,OAAO;cAAC+B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B5B,OAAA;cAAAwB,QAAA,EAAO1B,UAAU,CAACW,QAAQ;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCxB,OAAA;cAAIuB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAE3B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAClFnB,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5B,OAAA;UAAKuB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAE1ExB,OAAA;YAAKuB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BxB,OAAA;cAAIuB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAE3B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7F5B,OAAA;cAAGuB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAG,6FACfV,aAAa,GACT,6DAA6D,GAC7D,kDACL,EAAE;YAAAW,QAAA,gBACDxB,OAAA,CAACR,OAAO;cAAC+B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B5B,OAAA;cAAAwB,QAAA,EAAO1B,UAAU,CAACW,QAAQ;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAClFnB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5B,OAAA;UAAKuB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DxB,OAAA;YAAKuB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxB,OAAA;cAAIuB,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAE3B,UAAU,CAACe,SAAS,EAAE,MAAM;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzG5B,OAAA;cAAGuB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClCxB,OAAA;cAAKuB,SAAS,EAAG,sHACfV,aAAa,GACT,+DAA+D,GAC/D,oDACL,EAAE;cAAAW,QAAA,gBACDxB,OAAA,CAACR,OAAO;gBAAC+B,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C5B,OAAA;gBAAAwB,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB5B,OAAA;gBAAAwB,QAAA,EAAO1B,UAAU,CAACW,QAAQ;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BxB,OAAA;cAAKuB,SAAS,EAAC,mGAAmG;cAAAC,QAAA,GAAC,WACxG,EAACnB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKuB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFxB,OAAA;QAAKuB,SAAS,EAAC,6EAA6E;QAAAC,QAAA,eAC1FxB,OAAA,CAACT,MAAM,CAACkE,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BvC,SAAS,EAAC,0IAA0I;UAAAC,QAAA,gBAIpJxB,OAAA;YAAKuB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CxB,OAAA;cAAKuB,SAAS,EAAC,iNAAiN;cAAAC,QAAA,eAC9NxB,OAAA;gBAAAwB,QAAA,GAAM,WAAS,EAACnB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,qJAAqJ;YAAAC,QAAA,EACjKL,YAAY,CAACU;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAGLT,YAAY,CAAC8C,KAAK,iBACjBjE,OAAA;YAAKuB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFxB,OAAA;cAAKuB,SAAS,EAAC,+HAA+H;cAAAC,QAAA,eAC5IxB,OAAA;gBACEkE,GAAG,EAAE/C,YAAY,CAAC8C,KAAM;gBACxBE,GAAG,EAAC,UAAU;gBACd5C,SAAS,EAAC;cAAkI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD5B,OAAA;YAAKuB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BL,YAAY,CAACiC,IAAI,KAAK,KAAK,IAAIpB,MAAM,CAACC,IAAI,CAACd,YAAY,CAACY,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,GAAGJ,SAAS,CAAC,CAAC,GAAGqB,eAAe,CAAC;UAAC;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC;QAAA,GAnCDvB,aAAa;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoCR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKuB,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxExB,OAAA;QAAKuB,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElExB,OAAA;UAAKuB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChExB,OAAA;YACE+C,OAAO,EAAEpC,UAAW;YACpByD,QAAQ,EAAE/D,aAAa,KAAK,CAAE;YAC9BkB,SAAS,EAAG,oIACVlB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAmB,QAAA,gBAEHxB,OAAA,CAACP,WAAW;cAAC8B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC5B,OAAA;cAAAwB,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAGT5B,OAAA;YAAKuB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACP,UAAU,gBACVjB,OAAA;cAAKuB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHxB,OAAA;gBAAAwB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf5B,OAAA;gBAAAwB,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEN5B,OAAA;cAAKuB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHxB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B5B,OAAA;gBAAAwB,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5B,OAAA;YACE+C,OAAO,EAAErC,MAAO;YAChB0D,QAAQ,EAAE,CAACnD,UAAW;YACtBM,SAAS,EAAG,oIACV,CAACN,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAkB,QAAA,EAEFnB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B5B,OAAA;gBAAAwB,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACnB,CAAC,gBAEH5B,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB5B,OAAA,CAACN,YAAY;gBAAC6B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5B,OAAA;UAAKuB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1ExB,OAAA;YACE+C,OAAO,EAAEpC,UAAW;YACpByD,QAAQ,EAAE/D,aAAa,KAAK,CAAE;YAC9BkB,SAAS,EAAG,qIACVlB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAmB,QAAA,gBAEHxB,OAAA,CAACP,WAAW;cAAC8B,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC5B,OAAA;cAAAwB,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGT5B,OAAA;YAAKuB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACP,UAAU,gBACVjB,OAAA;cAAKuB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHxB,OAAA;gBAAAwB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf5B,OAAA;gBAAAwB,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEN5B,OAAA;cAAKuB,SAAS,EAAC,yGAAyG;cAAAC,QAAA,gBACtHxB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B5B,OAAA;gBAAAwB,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5B,OAAA;YACE+C,OAAO,EAAErC,MAAO;YAChB0D,QAAQ,EAAE,CAACnD,UAAW;YACtBM,SAAS,EAAG,qIACV,CAACN,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAkB,QAAA,EAEFnB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B5B,OAAA;gBAAAwB,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACnB,CAAC,gBAEH5B,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB5B,OAAA,CAACN,YAAY;gBAAC6B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5B,OAAA;UAAKuB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChExB,OAAA;YACE+C,OAAO,EAAEpC,UAAW;YACpByD,QAAQ,EAAE/D,aAAa,KAAK,CAAE;YAC9BkB,SAAS,EAAG,kKACVlB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6EACL,EAAE;YAAAmB,QAAA,gBAEHxB,OAAA,CAACP,WAAW;cAAC8B,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD5B,OAAA;cAAAwB,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGT5B,OAAA;YAAKuB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC,CAACP,UAAU,gBACVjB,OAAA;cAAKuB,SAAS,EAAC,sIAAsI;cAAAC,QAAA,gBACnJxB,OAAA;gBAAAwB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACf5B,OAAA;gBAAAwB,QAAA,EAAM;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,gBAEN5B,OAAA;cAAKuB,SAAS,EAAC,sIAAsI;cAAAC,QAAA,gBACnJxB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B5B,OAAA;gBAAAwB,QAAA,EAAM;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5B,OAAA;YACE+C,OAAO,EAAErC,MAAO;YAChB0D,QAAQ,EAAE,CAACnD,UAAW;YACtBM,SAAS,EAAG,kKACV,CAACN,UAAU,GACP,8CAA8C,GAC9CZ,aAAa,KAAKC,cAAc,GAAG,CAAC,GAClC,4EAA4E,GAC5E,0EACP,EAAE;YAAAkB,QAAA,EAEFnB,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCN,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA,CAACL,OAAO;gBAAC4B,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C5B,OAAA;gBAAAwB,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB,CAAC,gBAEH5B,OAAA,CAAAE,SAAA;cAAAsB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B5B,OAAA,CAACN,YAAY;gBAAC6B,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eAClD;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAncIX,YAAY;AAAAkE,EAAA,GAAZlE,YAAY;AAqclB,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}