{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport \"./index.css\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool, TbRocket, TbUserPlus, TbLogin } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Handle Try for Free modal\n  const handleTryForFree = () => {\n    setShowTryForFreeModal(true);\n  };\n  const handleTryForFreeSubmit = trialData => {\n    // Navigate to trial experience with user data\n    navigate('/trial', {\n      state: {\n        trialUserInfo: trialData\n      }\n    });\n    setShowTryForFreeModal(false);\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home relative min-h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(reviewsSectionRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(contactUsRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), user && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 via-blue-900 to-black\",\n          style: {\n            backgroundImage: `\n                radial-gradient(circle at 20% 30%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),\n                radial-gradient(circle at 40% 70%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),\n                radial-gradient(circle at 90% 80%, rgba(200, 119, 255, 0.3) 0%, transparent 50%),\n                radial-gradient(circle at 10% 90%, rgba(119, 255, 198, 0.2) 0%, transparent 50%)\n              `\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          children: [...Array(100)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute w-1 h-1 bg-white rounded-full opacity-70\",\n            style: {\n              top: `${Math.random() * 100}%`,\n              left: `${Math.random() * 100}%`\n            },\n            animate: {\n              opacity: [0.3, 1, 0.3],\n              scale: [0.5, 1, 0.5]\n            },\n            transition: {\n              duration: 2 + Math.random() * 3,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full opacity-10 blur-3xl\",\n          animate: {\n            x: [-50, 50, -50],\n            y: [-30, 30, -30],\n            scale: [1, 1.2, 1]\n          },\n          transition: {\n            duration: 20,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500 rounded-full opacity-15 blur-3xl\",\n          animate: {\n            x: [50, -50, 50],\n            y: [30, -30, 30],\n            scale: [1.2, 1, 1.2]\n          },\n          transition: {\n            duration: 25,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute top-1/2 left-1/2 w-72 h-72 bg-pink-500 rounded-full opacity-8 blur-3xl\",\n          animate: {\n            x: [-30, 30, -30],\n            y: [40, -40, 40],\n            scale: [1, 1.3, 1]\n          },\n          transition: {\n            duration: 30,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 container mx-auto px-4 py-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center min-h-[80vh]\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"text-center space-y-8 max-w-4xl mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.3\n              },\n              className: \"text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight drop-shadow-2xl\",\n              style: {\n                textShadow: '0 0 30px rgba(255,255,255,0.5), 0 0 60px rgba(255,255,255,0.3)'\n              },\n              children: \"Study Smarter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.7\n              },\n              className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 md:gap-8\",\n              children: !user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: handleTryForFree,\n                  className: \"group w-full sm:w-auto min-w-[160px] px-8 py-4 bg-white bg-opacity-20 backdrop-blur-md border border-white border-opacity-30 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbRocket, {\n                      className: \"w-5 h-5 group-hover:animate-bounce\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Try for Free\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/register\",\n                  className: \"w-full sm:w-auto\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    className: \"w-full sm:w-auto min-w-[160px] px-8 py-4 bg-blue-500 bg-opacity-20 backdrop-blur-md border border-blue-300 border-opacity-50 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(TbUserPlus, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 514,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Register Now\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 515,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"w-full sm:w-auto\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    className: \"w-full sm:w-auto min-w-[160px] px-8 py-4 bg-purple-500 bg-opacity-20 backdrop-blur-md border border-purple-300 border-opacity-50 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(TbLogin, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Login Now\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/dashboard\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Go to Dashboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"grid grid-cols-3 gap-8 pt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-blue-600\",\n                  children: \"10K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-purple-600\",\n                  children: \"500+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-green-600\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10 bg-white rounded-3xl shadow-2xl p-8\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Image1,\n                  alt: \"Students Learning\",\n                  className: \"w-full h-auto rounded-2xl\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"absolute -top-6 -right-6 bg-blue-500 text-white p-4 rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-8 h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                className: \"absolute -bottom-6 -left-6 bg-green-500 text-white p-4 rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-8 h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-5, 5, -5]\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                className: \"absolute top-1/2 -left-8 bg-purple-500 text-white p-3 rounded-xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl transform rotate-6 scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\",\n            icon: TbUsers,\n            color: \"from-blue-500 to-blue-600\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\",\n            icon: TbSchool,\n            color: \"from-green-500 to-green-600\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\",\n            icon: TbBook,\n            color: \"from-purple-500 to-purple-600\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\",\n            icon: TbTrophy,\n            color: \"from-orange-500 to-orange-600\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              scale: 1.05,\n              y: -5\n            },\n            className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n            children: \"Get in touch with us for any questions or support. We're here to help you succeed!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: \"Send us a Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"or\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-green-700 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-green-500 hover:border-green-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  placeholder: \"Your Full Name\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  placeholder: \"<EMAIL>\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"message\",\n                  placeholder: \"Tell us how we can help you...\",\n                  rows: \"5\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 17\n              }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-green-600 font-medium\",\n                children: responseMessage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"Other Ways to Reach Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-green-600\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 820,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"WhatsApp Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 825,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"+255 655 285 49\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 826,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-blue-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 832,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Email Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-purple-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Response Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Usually within 2 hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-indigo-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 854,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Dar es Salaam, Tanzania\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 860,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 871,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TryForFreeModal, {\n      isOpen: showTryForFreeModal,\n      onClose: () => setShowTryForFreeModal(false),\n      onSubmit: handleTryForFreeSubmit\n    }, showTryForFreeModal ? 'open' : 'closed', false, {\n      fileName: _jsxFileName,\n      lineNumber: 880,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"2+OooZ71GBUhaFkx9H6QmV/qdRg=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "TbRocket", "TbUserPlus", "<PERSON>b<PERSON><PERSON><PERSON>", "message", "useSelector", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "TryForFreeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "showTryForFreeModal", "setShowTryForFreeModal", "user", "state", "navigate", "handleTryForFree", "handleTryForFreeSubmit", "trialData", "trialUserInfo", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "isAdmin", "size", "showOnlineStatus", "class", "backgroundImage", "Array", "map", "_", "i", "Math", "random", "left", "h1", "button", "whileTap", "to", "whileInView", "viewport", "once", "number", "text", "icon", "stat", "index", "rating", "review", "_review$user", "h2", "p", "href", "rel", "fill", "viewBox", "d", "onSubmit", "type", "placeholder", "onChange", "required", "rows", "disabled", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbRocket,\r\n  TbUserPlus,\r\n  TbLogin\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Handle Try for Free modal\r\n  const handleTryForFree = () => {\r\n    setShowTryForFreeModal(true);\r\n  };\r\n\r\n  const handleTryForFreeSubmit = (trialData) => {\r\n    // Navigate to trial experience with user data\r\n    navigate('/trial', { state: { trialUserInfo: trialData } });\r\n    setShowTryForFreeModal(false);\r\n  };\r\n\r\n\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home relative min-h-screen overflow-hidden\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Reviews */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item text-sm md:text-base\">Reviews</button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Notifications + User Profile */}\r\n            <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n              {/* Contact Us Button */}\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item text-sm md:text-base\">Contact Us</button>\r\n              </div>\r\n\r\n              {/* Notification Bell */}\r\n              {user && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile Section */}\r\n              {user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* GALAXY HERO SECTION */}\r\n      <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\r\n        {/* Galaxy Background */}\r\n        <div className=\"absolute inset-0\">\r\n          {/* Main Galaxy Background */}\r\n          <div\r\n            className=\"absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 via-blue-900 to-black\"\r\n            style={{\r\n              backgroundImage: `\r\n                radial-gradient(circle at 20% 30%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),\r\n                radial-gradient(circle at 40% 70%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),\r\n                radial-gradient(circle at 90% 80%, rgba(200, 119, 255, 0.3) 0%, transparent 50%),\r\n                radial-gradient(circle at 10% 90%, rgba(119, 255, 198, 0.2) 0%, transparent 50%)\r\n              `\r\n            }}\r\n          />\r\n\r\n          {/* Animated Stars */}\r\n          <div className=\"absolute inset-0\">\r\n            {[...Array(100)].map((_, i) => (\r\n              <motion.div\r\n                key={i}\r\n                className=\"absolute w-1 h-1 bg-white rounded-full opacity-70\"\r\n                style={{\r\n                  top: `${Math.random() * 100}%`,\r\n                  left: `${Math.random() * 100}%`,\r\n                }}\r\n                animate={{\r\n                  opacity: [0.3, 1, 0.3],\r\n                  scale: [0.5, 1, 0.5]\r\n                }}\r\n                transition={{\r\n                  duration: 2 + Math.random() * 3,\r\n                  repeat: Infinity,\r\n                  delay: Math.random() * 2\r\n                }}\r\n              />\r\n            ))}\r\n          </div>\r\n\r\n          {/* Floating Nebula Clouds */}\r\n          <motion.div\r\n            className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full opacity-10 blur-3xl\"\r\n            animate={{\r\n              x: [-50, 50, -50],\r\n              y: [-30, 30, -30],\r\n              scale: [1, 1.2, 1]\r\n            }}\r\n            transition={{\r\n              duration: 20,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n          <motion.div\r\n            className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500 rounded-full opacity-15 blur-3xl\"\r\n            animate={{\r\n              x: [50, -50, 50],\r\n              y: [30, -30, 30],\r\n              scale: [1.2, 1, 1.2]\r\n            }}\r\n            transition={{\r\n              duration: 25,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n          <motion.div\r\n            className=\"absolute top-1/2 left-1/2 w-72 h-72 bg-pink-500 rounded-full opacity-8 blur-3xl\"\r\n            animate={{\r\n              x: [-30, 30, -30],\r\n              y: [40, -40, 40],\r\n              scale: [1, 1.3, 1]\r\n            }}\r\n            transition={{\r\n              duration: 30,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Overlay for Content */}\r\n        <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\r\n\r\n        {/* Transparent Overlay Content */}\r\n        <div className=\"relative z-10 container mx-auto px-4 py-20\">\r\n          <div className=\"flex items-center justify-center min-h-[80vh]\">\r\n            {/* Centered Transparent Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"text-center space-y-8 max-w-4xl mx-auto\"\r\n            >\r\n              {/* Study Smarter Text with Transparency */}\r\n              <motion.h1\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.3 }}\r\n                className=\"text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight drop-shadow-2xl\"\r\n                style={{\r\n                  textShadow: '0 0 30px rgba(255,255,255,0.5), 0 0 60px rgba(255,255,255,0.3)'\r\n                }}\r\n              >\r\n                Study Smarter\r\n              </motion.h1>\r\n\r\n              {/* Transparent Overlay Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.7 }}\r\n                className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 md:gap-8\"\r\n              >\r\n                {!user ? (\r\n                  <>\r\n                    {/* Try for Free Button - Transparent */}\r\n                    <motion.button\r\n                      onClick={handleTryForFree}\r\n                      className=\"group w-full sm:w-auto min-w-[160px] px-8 py-4 bg-white bg-opacity-20 backdrop-blur-md border border-white border-opacity-30 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbRocket className=\"w-5 h-5 group-hover:animate-bounce\" />\r\n                        <span>Try for Free</span>\r\n                      </div>\r\n                    </motion.button>\r\n\r\n                    {/* Register Button - Transparent */}\r\n                    <Link to=\"/register\" className=\"w-full sm:w-auto\">\r\n                      <motion.button\r\n                        className=\"w-full sm:w-auto min-w-[160px] px-8 py-4 bg-blue-500 bg-opacity-20 backdrop-blur-md border border-blue-300 border-opacity-50 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <div className=\"flex items-center justify-center space-x-2\">\r\n                          <TbUserPlus className=\"w-5 h-5\" />\r\n                          <span>Register Now</span>\r\n                        </div>\r\n                      </motion.button>\r\n                    </Link>\r\n\r\n                    {/* Login Button - Transparent */}\r\n                    <Link to=\"/login\" className=\"w-full sm:w-auto\">\r\n                      <motion.button\r\n                        className=\"w-full sm:w-auto min-w-[160px] px-8 py-4 bg-purple-500 bg-opacity-20 backdrop-blur-md border border-purple-300 border-opacity-50 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:bg-opacity-30 transition-all duration-300 hover:scale-105\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <div className=\"flex items-center justify-center space-x-2\">\r\n                          <TbLogin className=\"w-5 h-5\" />\r\n                          <span>Login Now</span>\r\n                        </div>\r\n                      </motion.button>\r\n                    </Link>\r\n                  </>\r\n                ) : (\r\n                  <Link to=\"/dashboard\">\r\n                    <motion.button\r\n                      className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbArrowBigRightLinesFilled className=\"w-5 h-5\" />\r\n                        <span>Go to Dashboard</span>\r\n                      </div>\r\n                    </motion.button>\r\n                  </Link>\r\n                )}\r\n              </motion.div>\r\n\r\n              {/* Stats */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"grid grid-cols-3 gap-8 pt-8\"\r\n              >\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-3xl font-bold text-blue-600\">10K+</div>\r\n                  <div className=\"text-sm text-gray-600\">Students</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-3xl font-bold text-purple-600\">500+</div>\r\n                  <div className=\"text-sm text-gray-600\">Courses</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-3xl font-bold text-green-600\">95%</div>\r\n                  <div className=\"text-sm text-gray-600\">Success Rate</div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Content - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative\"\r\n            >\r\n              <div className=\"relative\">\r\n                {/* Main Image */}\r\n                <div className=\"relative z-10 bg-white rounded-3xl shadow-2xl p-8\">\r\n                  <img\r\n                    src={Image1}\r\n                    alt=\"Students Learning\"\r\n                    className=\"w-full h-auto rounded-2xl\"\r\n                    loading=\"lazy\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\r\n                  className=\"absolute -top-6 -right-6 bg-blue-500 text-white p-4 rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbTrophy className=\"w-8 h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\r\n                  className=\"absolute -bottom-6 -left-6 bg-green-500 text-white p-4 rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbBook className=\"w-8 h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [-5, 5, -5] }}\r\n                  transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 }}\r\n                  className=\"absolute top-1/2 -left-8 bg-purple-500 text-white p-3 rounded-xl shadow-lg z-20\"\r\n                >\r\n                  <TbBrain className=\"w-6 h-6\" />\r\n                </motion.div>\r\n\r\n                {/* Background Decoration */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl transform rotate-6 scale-105\"></div>\r\n              </div>\r\n            </motion.div>\r\n\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\"\r\n            >\r\n              Contact Us\r\n            </motion.h2>\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-lg text-gray-600 max-w-2xl mx-auto\"\r\n            >\r\n              Get in touch with us for any questions or support. We're here to help you succeed!\r\n            </motion.p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n            {/* Contact Form */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\"\r\n            >\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800\">Send us a Message</h3>\r\n                <div className=\"text-sm text-gray-500\">or</div>\r\n                <a\r\n                  href=\"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-green-700 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-green-500 hover:border-green-600\"\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"/>\r\n                  </svg>\r\n                  <span className=\"hidden sm:inline\">WhatsApp</span>\r\n                </a>\r\n              </div>\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    placeholder=\"Your Full Name\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    placeholder=\"<EMAIL>\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Message</label>\r\n                  <textarea\r\n                    name=\"message\"\r\n                    placeholder=\"Tell us how we can help you...\"\r\n                    rows=\"5\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\"\r\n                    value={formData.message}\r\n                    onChange={handleChange}\r\n                    required\r\n                  ></textarea>\r\n                </div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n                {responseMessage && (\r\n                  <p className=\"text-center text-green-600 font-medium\">\r\n                    {responseMessage}\r\n                  </p>\r\n                )}\r\n              </form>\r\n            </motion.div>\r\n\r\n            {/* Contact Information */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"space-y-8\"\r\n            >\r\n              {/* Contact Methods */}\r\n              <div className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Other Ways to Reach Us</h3>\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">WhatsApp Support</p>\r\n                      <p className=\"text-gray-600\">+255 655 285 49</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Email Support</p>\r\n                      <p className=\"text-gray-600\"><EMAIL></p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Response Time</p>\r\n                      <p className=\"text-gray-600\">Usually within 2 hours</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-indigo-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Location</p>\r\n                      <p className=\"text-gray-600\">Dar es Salaam, Tanzania</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n\r\n      {/* Try for Free Modal */}\r\n      <TryForFreeModal\r\n        key={showTryForFreeModal ? 'open' : 'closed'}\r\n        isOpen={showTryForFreeModal}\r\n        onClose={() => setShowTryForFreeModal(false)}\r\n        onSubmit={handleTryForFreeSubmit}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEzE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,iBAAiB,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM+B,YAAY,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IAAEmC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEnB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAE2C;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGxC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMK,sBAAsB,GAAIC,SAAS,IAAK;IAC5C;IACAH,QAAQ,CAAC,QAAQ,EAAE;MAAED,KAAK,EAAE;QAAEK,aAAa,EAAED;MAAU;IAAE,CAAC,CAAC;IAC3DN,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAID,MAAMQ,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC7B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAG2B;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAM0B,IAAI,GAAG,MAAM9C,SAAS,CAACa,QAAQ,CAAC;MACtC,IAAIiC,IAAI,CAACC,OAAO,EAAE;QAChBlD,OAAO,CAACkD,OAAO,CAAC,4BAA4B,CAAC;QAC7C3B,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEnB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLuB,kBAAkB,CAAC0B,IAAI,CAACjD,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACd5B,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEb,OAAA;IAAK4C,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzD7C,OAAA,CAACnB,MAAM,CAACiE,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K7C,OAAA;QAAK4C,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D7C,OAAA;UAAK4C,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBAErF7C,OAAA;YAAK4C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C7C,OAAA;cAAK4C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE7C,OAAA;gBAAQmD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACnB,iBAAiB,CAAE;gBAACsC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtD7C,OAAA;gBACE4C,SAAS,EAAC,wEAAwE;gBAClFiB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF7C,OAAA;kBACEgE,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAAC0B,GAAG,GAAG,8GAA8G;oBAC7H5B,CAAC,CAACE,MAAM,CAAC8B,OAAO,GAAG,MAAM;sBACvB;sBACAhC,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BjC,CAAC,CAACE,MAAM,CAACgC,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNvD,OAAA;gBAAK4C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C7C,OAAA;kBAAI4C,SAAS,EAAC,qFAAqF;kBAC/FiB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA5B,QAAA,gBAEJ7C,OAAA,CAACnB,MAAM,CAAC6F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,OAGC,eACA7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;sBACTZ,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdvD,OAAA,CAACnB,MAAM,CAAC6F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVZ,CAAC,EAAE;wBACDW,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,MAGC,eACA7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;sBACTZ,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;kBACTZ,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEb,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPY,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,CAAC;oBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNvD,OAAA;gBACE4C,SAAS,EAAC,gEAAgE;gBAC1EiB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,gBAEF7C,OAAA;kBACEgE,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BjC,CAAC,CAACE,MAAM,CAACiD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvD,OAAA;kBACE4C,SAAS,EAAC,gHAAgH;kBAC1HiB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA3C,QAAA,EACH;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA;gBAAK4C,SAAS,EAAC;cAAyK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNvD,OAAA;YAAK4C,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnE7C,OAAA;cAAK4C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE7C,OAAA;gBAAQmD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAClB,YAAY,CAAE;gBAACqC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,EAGLrC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuE,OAAO,kBACrBzF,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAf,QAAA,eAE1C7C,OAAA,CAACJ,gBAAgB;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGArC,IAAI,iBACHlB,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7C7C,OAAA,CAACH,cAAc;gBACbqB,IAAI,EAAEA,IAAK;gBACXwE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB9B,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFvD,OAAA;gBAAK4C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC7C,OAAA;kBAAK4C,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;gBAAM;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNvD,OAAA;kBAAK4C,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,GAAC,QACzF,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,KAAK;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhBvD,OAAA;MAAS4C,SAAS,EAAC,wEAAwE;MAAAC,QAAA,gBAEzF7C,OAAA;QAAK4C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE/B7C,OAAA;UACE4C,SAAS,EAAC,yFAAyF;UACnGiB,KAAK,EAAE;YACLgC,eAAe,EAAG;AAChC;AACA;AACA;AACA;AACA;AACA;UACY;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGFvD,OAAA;UAAK4C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9B,CAAC,GAAGiD,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACxBjG,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YAETZ,SAAS,EAAC,mDAAmD;YAC7DiB,KAAK,EAAE;cACL5B,GAAG,EAAG,GAAEiE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;cAC9BC,IAAI,EAAG,GAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI;YAC/B,CAAE;YACFjD,OAAO,EAAE;cACPD,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;cACtBQ,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;YACrB,CAAE;YACFC,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC,GAAGuC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;cAC/BtB,MAAM,EAAEC,QAAQ;cAChBlB,KAAK,EAAEsC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;YACzB;UAAE,GAdGF,CAAC;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeP,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;UACTZ,SAAS,EAAC,oFAAoF;UAC9FM,OAAO,EAAE;YACPyB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACjB3B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACjBS,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE;UACR;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;UACTZ,SAAS,EAAC,sFAAsF;UAChGM,OAAO,EAAE;YACPyB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAChB3B,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAChBS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;UACrB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE;UACR;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;UACTZ,SAAS,EAAC,iFAAiF;UAC3FM,OAAO,EAAE;YACPyB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACjB3B,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAChBS,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;UACnB,CAAE;UACFC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZkB,MAAM,EAAEC,QAAQ;YAChBC,IAAI,EAAE;UACR;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNvD,OAAA;QAAK4C,SAAS,EAAC;MAAyC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG/DvD,OAAA;QAAK4C,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzD7C,OAAA;UAAK4C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAE5D7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BE,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BU,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bf,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBAGnD7C,OAAA,CAACnB,MAAM,CAACwH,EAAE;cACRtD,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,qFAAqF;cAC/FiB,KAAK,EAAE;gBACLe,UAAU,EAAE;cACd,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAGZvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,+EAA+E;cAAAC,QAAA,EAExF,CAAC3B,IAAI,gBACJlB,OAAA,CAAAE,SAAA;gBAAA2C,QAAA,gBAEE7C,OAAA,CAACnB,MAAM,CAACyH,MAAM;kBACZnD,OAAO,EAAE9B,gBAAiB;kBAC1BuB,SAAS,EAAC,4PAA4P;kBACtQoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B8C,QAAQ,EAAE;oBAAE9C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,eAE1B7C,OAAA;oBAAK4C,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD7C,OAAA,CAACX,QAAQ;sBAACuD,SAAS,EAAC;oBAAoC;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3DvD,OAAA;sBAAA6C,QAAA,EAAM;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAGhBvD,OAAA,CAACtB,IAAI;kBAAC8H,EAAE,EAAC,WAAW;kBAAC5D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/C7C,OAAA,CAACnB,MAAM,CAACyH,MAAM;oBACZ1D,SAAS,EAAC,4PAA4P;oBACtQoC,UAAU,EAAE;sBAAEvB,KAAK,EAAE;oBAAK,CAAE;oBAC5B8C,QAAQ,EAAE;sBAAE9C,KAAK,EAAE;oBAAK,CAAE;oBAAAZ,QAAA,eAE1B7C,OAAA;sBAAK4C,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,gBACzD7C,OAAA,CAACV,UAAU;wBAACsD,SAAS,EAAC;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAClCvD,OAAA;wBAAA6C,QAAA,EAAM;sBAAY;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGPvD,OAAA,CAACtB,IAAI;kBAAC8H,EAAE,EAAC,QAAQ;kBAAC5D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC5C7C,OAAA,CAACnB,MAAM,CAACyH,MAAM;oBACZ1D,SAAS,EAAC,gQAAgQ;oBAC1QoC,UAAU,EAAE;sBAAEvB,KAAK,EAAE;oBAAK,CAAE;oBAC5B8C,QAAQ,EAAE;sBAAE9C,KAAK,EAAE;oBAAK,CAAE;oBAAAZ,QAAA,eAE1B7C,OAAA;sBAAK4C,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,gBACzD7C,OAAA,CAACT,OAAO;wBAACqD,SAAS,EAAC;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/BvD,OAAA;wBAAA6C,QAAA,EAAM;sBAAS;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA,eACP,CAAC,gBAEHvD,OAAA,CAACtB,IAAI;gBAAC8H,EAAE,EAAC,YAAY;gBAAA3D,QAAA,eACnB7C,OAAA,CAACnB,MAAM,CAACyH,MAAM;kBACZ1D,SAAS,EAAC,gKAAgK;kBAC1KoC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5B8C,QAAQ,EAAE;oBAAE9C,KAAK,EAAE;kBAAK,CAAE;kBAAAZ,QAAA,eAE1B7C,OAAA;oBAAK4C,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzD7C,OAAA,CAAClB,0BAA0B;sBAAC8D,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClDvD,OAAA;sBAAA6C,QAAA,EAAM;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGbvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAEvC7C,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7C,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DvD,OAAA;kBAAK4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7C,OAAA;kBAAK4C,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9DvD,OAAA;kBAAK4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7C,OAAA;kBAAK4C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DvD,OAAA;kBAAK4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,UAAU;YAAAC,QAAA,eAEpB7C,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAEvB7C,OAAA;gBAAK4C,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChE7C,OAAA;kBACEgE,GAAG,EAAEtE,MAAO;kBACZuE,GAAG,EAAC,mBAAmB;kBACvBrB,SAAS,EAAC,2BAA2B;kBACrChC,OAAO,EAAC;gBAAM;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE;gBAAY,CAAE;gBACjEnC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAE1F7C,OAAA,CAACf,QAAQ;kBAAC2D,SAAS,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAEbvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAE,CAAE;gBAC3EhB,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAE7F7C,OAAA,CAAChB,MAAM;kBAAC4D,SAAS,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAEbvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAE;gBAC5BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAI,CAAE;gBAC7EhB,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,eAE3F7C,OAAA,CAACjB,OAAO;kBAAC6D,SAAS,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGbvD,OAAA;gBAAK4C,SAAS,EAAC;cAAyG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS4C,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvF7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/ByD,WAAW,EAAE;YAAExD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B+C,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB/D,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAEzD,CACC;YAAE+D,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE5H,OAAO;YAAEgG,KAAK,EAAE;UAA4B,CAAC,EAC9F;YAAE0B,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAE1H,QAAQ;YAAE8F,KAAK,EAAE;UAA8B,CAAC,EACjG;YAAE0B,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE,eAAe;YAAEC,IAAI,EAAE9H,MAAM;YAAEkG,KAAK,EAAE;UAAgC,CAAC,EAChG;YAAE0B,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE,cAAc;YAAEC,IAAI,EAAE7H,QAAQ;YAAEiG,KAAK,EAAE;UAAgC,CAAC,CAChG,CAACa,GAAG,CAAC,CAACgB,IAAI,EAAEC,KAAK,kBAChBhH,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YAETT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,EAAE;cAAES,KAAK,EAAE;YAAI,CAAE;YAC3CgD,WAAW,EAAE;cAAExD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,CAAC;cAAES,KAAK,EAAE;YAAE,CAAE;YAC5CC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEoD,KAAK,GAAG;YAAI,CAAE;YAClDN,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB3B,UAAU,EAAE;cAAEvB,KAAK,EAAE,IAAI;cAAET,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCJ,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAE1I7C,OAAA;cAAK4C,SAAS,EAAG,gFAA+EmE,IAAI,CAAC7B,KAAM,2FAA2F;cAAArC,QAAA,eACpM7C,OAAA,CAAC+G,IAAI,CAACD,IAAI;gBAAClE,SAAS,EAAC;cAAkC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNvD,OAAA;cAAK4C,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EAAEkE,IAAI,CAACH;YAAM;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1GvD,OAAA;cAAK4C,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAEkE,IAAI,CAACF;YAAI;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAZvFyD,KAAK;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS0B,GAAG,EAAEpB,iBAAkB;MAACsC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1D7C,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACEoE,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,yIAAyI;YAC/I3F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACEuG,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,uIAAuI;YAC7I3F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACEuG,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,mHAAmH;YACzH3F,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAACqF,GAAG,CAAC,CAACmB,MAAM,EAAEF,KAAK;YAAA,IAAAG,YAAA;YAAA,oBAClBnH,OAAA,CAACnB,MAAM,CAAC2E,GAAG;cAETT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/ByD,WAAW,EAAE;gBAAExD,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEoD,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB/D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB7C,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B7C,OAAA;kBAAK6D,KAAK,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEM,QAAQ,EAAE;kBAAU,CAAE;kBAAA3C,QAAA,EACnD,GAAG,CAACgC,MAAM,CAACqC,MAAM,CAACD,MAAM;gBAAC;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAACqE,MAAM,CAACL,IAAI,EAAC,IAAC;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDvD,OAAA;gBAAK4C,SAAS,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCvD,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAsE,YAAA,GAAED,MAAM,CAAChG,IAAI,cAAAiG,YAAA,uBAAXA,YAAA,CAAazG;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnDyD,KAAK;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS0B,GAAG,EAAEnB,YAAa;MAACqC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC9F7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD7C,OAAA;UAAK4C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7C,OAAA,CAACnB,MAAM,CAACuI,EAAE;YACRrE,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/ByD,WAAW,EAAE;cAAExD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B+C,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB/D,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9D;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZvD,OAAA,CAACnB,MAAM,CAACwI,CAAC;YACPtE,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/ByD,WAAW,EAAE;cAAExD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1C8C,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB/D,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACpD;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENvD,OAAA;UAAK4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD7C,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC8B,WAAW,EAAE;cAAExD,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B+C,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB/D,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBAErD7C,OAAA;cAAK4C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7C,OAAA;gBAAI4C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEvD,OAAA;gBAAK4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/CvD,OAAA;gBACEsH,IAAI,EAAC,6GAA6G;gBAClHhF,MAAM,EAAC,QAAQ;gBACfiF,GAAG,EAAC,qBAAqB;gBACzB3E,SAAS,EAAC,qOAAqO;gBAAAC,QAAA,gBAE/O7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAAC4E,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAA5E,QAAA,eAC9D7C,OAAA;oBAAM0H,CAAC,EAAC;kBAAklC;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACNvD,OAAA;kBAAM4C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNvD,OAAA;cAAM2H,QAAQ,EAAEpF,YAAa;cAACK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACjD7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5EvD,OAAA;kBACE4H,IAAI,EAAC,MAAM;kBACXlH,IAAI,EAAC,MAAM;kBACXmH,WAAW,EAAC,gBAAgB;kBAC5BjF,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAE7B,QAAQ,CAACE,IAAK;kBACrBoH,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvD,OAAA;kBACE4H,IAAI,EAAC,OAAO;kBACZlH,IAAI,EAAC,OAAO;kBACZmH,WAAW,EAAC,wBAAwB;kBACpCjF,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAE7B,QAAQ,CAACG,KAAM;kBACtBmH,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EvD,OAAA;kBACEU,IAAI,EAAC,SAAS;kBACdmH,WAAW,EAAC,gCAAgC;kBAC5CG,IAAI,EAAC,GAAG;kBACRpF,SAAS,EAAC,yIAAyI;kBACnJP,KAAK,EAAE7B,QAAQ,CAAChB,OAAQ;kBACxBsI,QAAQ,EAAE3F,YAAa;kBACvB4F,QAAQ;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNvD,OAAA;gBACE4H,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAErH,OAAQ;gBAClBgC,SAAS,EAAC,2OAA2O;gBAAAC,QAAA,EAEpPjC,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACRzC,eAAe,iBACdd,OAAA;gBAAG4C,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAClD/B;cAAe;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGbvD,OAAA,CAACnB,MAAM,CAAC2E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/B8B,WAAW,EAAE;cAAExD,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9B+C,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB/D,SAAS,EAAC,WAAW;YAAAC,QAAA,eAGrB7C,OAAA;cAAK4C,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD7C,OAAA;gBAAI4C,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFvD,OAAA;gBAAK4C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB7C,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7C,OAAA;oBAAK4C,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF7C,OAAA;sBAAK4C,SAAS,EAAC,wBAAwB;sBAAC4E,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAA5E,QAAA,eAC7E7C,OAAA;wBAAM0H,CAAC,EAAC;sBAAklC;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzlC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG4C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7DvD,OAAA;sBAAG4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7C,OAAA;oBAAK4C,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF7C,OAAA;sBAAK4C,SAAS,EAAC,uBAAuB;sBAAC4E,IAAI,EAAC,MAAM;sBAACU,MAAM,EAAC,cAAc;sBAACT,OAAO,EAAC,WAAW;sBAAA5E,QAAA,eAC1F7C,OAAA;wBAAMmI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACX,CAAC,EAAC;sBAAsG;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1K;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG4C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DvD,OAAA;sBAAG4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7C,OAAA;oBAAK4C,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF7C,OAAA;sBAAK4C,SAAS,EAAC,yBAAyB;sBAAC4E,IAAI,EAAC,MAAM;sBAACU,MAAM,EAAC,cAAc;sBAACT,OAAO,EAAC,WAAW;sBAAA5E,QAAA,eAC5F7C,OAAA;wBAAMmI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACX,CAAC,EAAC;sBAA6C;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG4C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DvD,OAAA;sBAAG4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C7C,OAAA;oBAAK4C,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF7C,OAAA;sBAAK4C,SAAS,EAAC,yBAAyB;sBAAC4E,IAAI,EAAC,MAAM;sBAACU,MAAM,EAAC,cAAc;sBAACT,OAAO,EAAC,WAAW;sBAAA5E,QAAA,gBAC5F7C,OAAA;wBAAMmI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACX,CAAC,EAAC;sBAAoF;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC3JvD,OAAA;wBAAMmI,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACX,CAAC,EAAC;sBAAkC;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG4C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrDvD,OAAA;sBAAG4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAQ4C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB7C,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B7C,OAAA;UAAG4C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTvD,OAAA,CAACF,eAAe;MAEdwI,MAAM,EAAEtH,mBAAoB;MAC5BuH,OAAO,EAAEA,CAAA,KAAMtH,sBAAsB,CAAC,KAAK,CAAE;MAC7C0G,QAAQ,EAAErG;IAAuB,GAH5BN,mBAAmB,GAAG,MAAM,GAAG,QAAQ;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAI7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnD,EAAA,CA/1BID,IAAI;EAAA,QAQSV,WAAW,EACXb,WAAW;AAAA;AAAA4J,EAAA,GATxBrI,IAAI;AAi2BV,eAAeA,IAAI;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}